import React, { useState } from 'react';
import ToolSelectionButtons from './ToolSelectionButtons';
import { CircularaddbtnIcon, GeneratebtnIcon, ImagetoImageModalIcon, ImagetoVideoModalIcon, SelectArrow } from './icons/icons';

type Mode = 'text-to-image' | 'image-to-image' | 'text-to-video' | 'image-to-video' | 'text-to-speech' | 'music-generation' | 'sound-effects' | 'lip-sync';

interface ChatInputControlsProps {
  prompt: string;
  setPrompt: (v: string) => void;
  selectedModel: string;
  setSelectedModel: (v: string) => void;
  selectedStyle: string;
  setSelectedStyle: (v: string) => void;
  selectedRatio: string;
  setSelectedRatio: (v: string) => void;
  isGenerating: boolean;
  handleGenerate: (image?: string) => void;
  activeMode: Mode;
  handleModeChange: (mode: Mode) => void;
  videoDuration: number;
  setVideoDuration: (v: number) => void;
  videoAspectRatio: string;
  setVideoAspectRatio: (v: string) => void;
  selectedVideoModel: string;
  handleVideoModelChange: (v: string) => void;
  getModelOptions: (model: string) => { durations: number[]; aspectRatios: string[] };
  handlePlusButtonClick: () => void;
  selectedVoice: string;
  setSelectedVoice: (v: string) => void;
  speechSpeed: number;
  setSpeechSpeed: (v: number) => void;
  musicBitrate: number;
  setMusicBitrate: (v: number) => void;
  musicSampleRate: number;
  setMusicSampleRate: (v: number) => void;
  soundEffectModel: string;
  setSoundEffectModel: (v: string) => void;
  soundEffectSteps: number;
  setSoundEffectSteps: (v: number) => void;
  soundEffectGuidance: number;
  setSoundEffectGuidance: (v: number) => void;
  numberOfOutputs: number;
  setNumberOfOutputs: (v: number) => void;
  uploadedImage: string | null;
  setUploadedImage: (v: string | null) => void;
  collapsed?: boolean;
}

const ChatInputControls: React.FC<ChatInputControlsProps> = ({
  prompt,
  setPrompt,
  selectedModel,
  setSelectedModel,
  selectedStyle,
  setSelectedStyle,
  selectedRatio,
  setSelectedRatio,
  isGenerating,
  handleGenerate,
  activeMode,
  handleModeChange,
  videoDuration,
  setVideoDuration,
  videoAspectRatio,
  setVideoAspectRatio,
  selectedVideoModel,
  handleVideoModelChange,
  getModelOptions,
  handlePlusButtonClick,
  selectedVoice,
  setSelectedVoice,
  speechSpeed,
  setSpeechSpeed,
  musicBitrate,
  setMusicBitrate,
  musicSampleRate,
  setMusicSampleRate,
  soundEffectModel,
  setSoundEffectModel,
  soundEffectSteps,
  setSoundEffectSteps,
  soundEffectGuidance,
  setSoundEffectGuidance,
  numberOfOutputs,
  setNumberOfOutputs,
  uploadedImage,
  setUploadedImage,
  collapsed = false,
}) => {
  // Update handleImageUpload to use setUploadedImage from props
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        setUploadedImage(event.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Function to get mode-specific content
  const getModeContent = (mode: Mode) => {
    switch (mode) {
      case 'text-to-image':
        return {
          title: 'Text to Image',
          description: 'Turn written prompts to Images'
        };
      case 'image-to-image':
        return {
          title: 'Image to Image',
          description: 'Turn Image to a Restyled Image'
        };
      case 'text-to-video':
        return {
          title: 'Text to Video',
          description: 'Turn written prompts to Video'
        };
      case 'image-to-video':
        return {
          title: 'Image to Video',
          description: 'Animate images into dynamic videos'
        };


      case 'text-to-speech':
        return {
          title: 'Text to Speech',
          description: 'Convert text into natural speech'
        };
      case 'music-generation':
        return {
          title: 'Music Generation',
          description: 'Create music from prompts and references'
        };
      case 'sound-effects':
        return {
          title: 'Sound Effects',
          description: 'Generate custom sound effects'
        };
      case 'lip-sync':
        return {
          title: 'Lip Sync',
          description: 'Synchronize speech with lip movements'
        };
      default:
        return {
          title: 'AI Generation',
          description: 'Create content with AI'
        };
    }
  };

  const modeContent = getModeContent(activeMode);
  return (
    <div>
      {!collapsed && (
        <div className={`relative transition-all duration-300 ease-in-out ${activeMode === 'lip-sync' ? 'bottom-4' :
          activeMode === 'music-generation' ? 'top-10' :
            'top-10'
          } left-2`}>
          <h1 className=' text-xl font-bold text-[#887DFF]  rounded-md'>{modeContent.title}</h1>
          <p className='text-[#D1D1D1] text-base top-1 relative'>{modeContent.description}</p>
        </div>
      )}
      {/* Controls Row */}
      {/* Controls Row */}
      {activeMode !== 'lip-sync' && !collapsed && (
        <div className="flex flex-wrap gap-2 sm:gap-3 md:gap-4 mb-3 sm:mb-4 justify-end transition-all duration-300 ease-in-out">
          {activeMode === 'text-to-image' && (
            <div className="relative">
              <div
                className="rounded-full p-[0.5px]"
                style={{
                  background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                }}
              >
                <select
                  id="numberOfOutputs"
                  value={numberOfOutputs}
                  onChange={(e) => setNumberOfOutputs(Number(e.target.value))}
                  className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[100px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                >
                  <option value={1}>1 Output</option>
                  <option value={2}>2 Outputs</option>
                  <option value={3}>3 Outputs</option>
                  <option value={4}>4 Outputs</option>
                </select>
              </div>
              <SelectArrow />
            </div>
          )}

          {activeMode === 'text-to-speech' ? (
            <>
              <div className="relative">
                <div
                  className="rounded-full p-[0.5px]"
                  style={{
                    background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                  }}
                >
                  <select
                    value={selectedVoice}
                    onChange={(e) => setSelectedVoice(e.target.value)}
                    className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[120px] sm:min-w-[140px] md:min-w-[160px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                  >
                    <optgroup label="American Female">
                      <option value="af_alloy">Alloy</option>
                      <option value="af_aoede">Aoede</option>
                      <option value="af_bella">Bella</option>
                      <option value="af_jessica">Jessica</option>
                      <option value="af_kore">Kore</option>
                      <option value="af_nicole">Nicole</option>
                      <option value="af_nova">Nova</option>
                      <option value="af_river">River</option>
                      <option value="af_sarah">Sarah</option>
                      <option value="af_sky">Sky</option>
                    </optgroup>
                    <optgroup label="American Male">
                      <option value="am_adam">Adam</option>
                      <option value="am_echo">Echo</option>
                      <option value="am_eric">Eric</option>
                      <option value="am_fenrir">Fenrir</option>
                      <option value="am_liam">Liam</option>
                      <option value="am_michael">Michael</option>
                      <option value="am_onyx">Onyx</option>
                      <option value="am_puck">Puck</option>
                    </optgroup>
                    <optgroup label="British Female">
                      <option value="bf_alice">Alice</option>
                      <option value="bf_emma">Emma</option>
                      <option value="bf_isabella">Isabella</option>
                      <option value="bf_lily">Lily</option>
                    </optgroup>
                    <optgroup label="British Male">
                      <option value="bm_daniel">Daniel</option>
                      <option value="bm_fable">Fable</option>
                      <option value="bm_george">George</option>
                      <option value="bm_lewis">Lewis</option>
                    </optgroup>
                    <optgroup label="Other Languages">
                      <option value="ff_siwis">Siwis (French)</option>
                      <option value="jf_alpha">Alpha (Japanese)</option>
                      <option value="jf_gongitsune">Gongitsune (Japanese)</option>
                      <option value="jf_nezumi">Nezumi (Japanese)</option>
                      <option value="jf_tebukuro">Tebukuro (Japanese)</option>
                      <option value="jm_kumo">Kumo (Japanese Male)</option>
                      <option value="zf_xiaobei">Xiaobei (Chinese)</option>
                      <option value="zf_xiaoni">Xiaoni (Chinese)</option>
                      <option value="zf_xiaoxiao">Xiaoxiao (Chinese)</option>
                      <option value="zf_xiaoyi">Xiaoyi (Chinese)</option>
                      <option value="zm_yunjian">Yunjian (Chinese Male)</option>
                      <option value="zm_yunxi">Yunxi (Chinese Male)</option>
                      <option value="zm_yunxia">Yunxia (Chinese Male)</option>
                      <option value="zm_yunyang">Yunyang (Chinese Male)</option>
                    </optgroup>
                  </select>
                </div>
                <SelectArrow />
              </div>
              <div className="relative">
                <div
                  className="rounded-full p-[0.5px]"
                  style={{
                    background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                  }}
                >
                  <select
                    value={speechSpeed}
                    onChange={(e) => setSpeechSpeed(Number(e.target.value))}
                    className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[100px] sm:min-w-[120px] md:min-w-[140px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                  >
                    <option value={0.1}>0.1x Speed</option>
                    <option value={0.25}>0.25x Speed</option>
                    <option value={0.5}>0.5x Speed</option>
                    <option value={0.75}>0.75x Speed</option>
                    <option value={1.0}>1.0x Speed (Normal)</option>
                    <option value={1.25}>1.25x Speed</option>
                    <option value={1.5}>1.5x Speed</option>
                    <option value={2.0}>2.0x Speed</option>
                    <option value={2.5}>2.5x Speed</option>
                    <option value={3.0}>3.0x Speed</option>
                    <option value={4.0}>4.0x Speed</option>
                    <option value={5.0}>5.0x Speed</option>
                  </select>
                </div>
                <SelectArrow />
              </div>
            </>
          ) : activeMode === 'music-generation' ? (
            <>
              <div className="relative">
                <div
                  className="rounded-full p-[0.5px]"
                  style={{
                    background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                  }}
                >
                  <select
                    value={musicBitrate}
                    onChange={(e) => setMusicBitrate(Number(e.target.value))}
                    className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[120px] sm:min-w-[140px] md:min-w-[160px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                  >
                    <option value={32000}>32 kbps</option>
                    <option value={64000}>64 kbps</option>
                    <option value={128000}>128 kbps</option>
                    <option value={256000}>256 kbps (High Quality)</option>
                  </select>
                </div>
                <SelectArrow />
              </div>
              <div className="relative">
                <div
                  className="rounded-full p-[0.5px]"
                  style={{
                    background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                  }}
                >
                  <select
                    value={musicSampleRate}
                    onChange={(e) => setMusicSampleRate(Number(e.target.value))}
                    className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[100px] sm:min-w-[120px] md:min-w-[140px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                  >
                    <option value={16000}>16 kHz</option>
                    <option value={24000}>24 kHz</option>
                    <option value={32000}>32 kHz</option>
                    <option value={44100}>44.1 kHz (CD Quality)</option>
                  </select>
                </div>
                <SelectArrow />
              </div>
            </>
          ) : activeMode === 'sound-effects' ? (
            <>
              <div className="relative">
                <div
                  className="rounded-full p-[0.5px]"
                  style={{
                    background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                  }}
                >
                  <select
                    value={soundEffectModel}
                    onChange={(e) => setSoundEffectModel(e.target.value)}
                    className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[120px] sm:min-w-[140px] md:min-w-[160px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                  >
                    <option value="tango2">Tango 2</option>
                    <option value="tango2-full">Tango 2 Full</option>
                  </select>
                </div>
                <SelectArrow />
              </div>
              <div className="relative">
                <div
                  className="rounded-full p-[0.5px]"
                  style={{
                    background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                  }}
                >
                  <select
                    value={soundEffectSteps}
                    onChange={(e) => setSoundEffectSteps(Number(e.target.value))}
                    className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[100px] sm:min-w-[120px] md:min-w-[140px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                  >
                    <option value={50}>50 Steps</option>
                    <option value={100}>100 Steps</option>
                    <option value={150}>150 Steps</option>
                    <option value={200}>200 Steps</option>
                  </select>
                </div>
                <SelectArrow />
              </div>
              <div className="relative">
                <div
                  className="rounded-full p-[0.5px]"
                  style={{
                    background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                  }}
                >
                  <select
                    value={soundEffectGuidance}
                    onChange={(e) => setSoundEffectGuidance(Number(e.target.value))}
                    className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[100px] sm:min-w-[120px] md:min-w-[140px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                  >
                    <option value={1}>1.0 Guidance</option>
                    <option value={2}>2.0 Guidance</option>
                    <option value={3}>3.0 Guidance</option>
                    <option value={4}>4.0 Guidance</option>
                    <option value={5}>5.0 Guidance</option>
                  </select>
                </div>
                <SelectArrow />
              </div>
            </>
          ) : (activeMode === 'text-to-image' || activeMode === 'image-to-image') ? (
            <div className="relative">
              <div
                className="rounded-full p-[0.5px]"
                style={{
                  background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                }}
              >
                <select
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[120px] sm:min-w-[140px] md:min-w-[160px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                >
                  <option value="flux-1.1-pro-ultra">Flux 1.1 Pro Ultra</option>
                  <option value="flux-dev-lora">Flux Dev LoRA</option>
                  <option value="flux-kontext-max">Flux Kontext Max</option>
                </select>
              </div>
              <SelectArrow />
            </div>
          ) : (
            <>
              <div className="relative">
                <div
                  className="rounded-full p-[0.5px]"
                  style={{
                    background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                  }}
                >
                  <select
                    value={selectedVideoModel}
                    onChange={(e) => handleVideoModelChange(e.target.value)}
                    className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[120px] sm:min-w-[140px] md:min-w-[160px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                  >
                    <option value="kling-v1.6-standard">Kling v1.6 Standard</option>
                    <option value="google-veo-3">Google Veo 3</option>
                    <option value="minimax-video-01">MiniMax Video-01</option>
                    {activeMode === 'text-to-video' && (
                      <>
                        <option value="wan-2.1-t2v-480p">Wan 2.1 T2V (480p)</option>
                        <option value="wan-2.1-t2v-720p">Wan 2.1 T2V (720p)</option>
                      </>
                    )}
                    {activeMode === 'image-to-video' && (
                      <>
                        <option value="wan-2.1-i2v-480p">Wan 2.1 I2V (480p)</option>
                        <option value="wan-2.1-i2v-720p">Wan 2.1 I2V (720p)</option>
                      </>
                    )}
                  </select>
                </div>
                <SelectArrow />
              </div>

              {/* Video Duration Control */}
              <div className="relative">
                <div
                  className="rounded-full p-[0.5px]"
                  style={{
                    background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                  }}
                >
                  <select
                    value={videoDuration}
                    onChange={(e) => setVideoDuration(Number(e.target.value))}
                    className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[100px] sm:min-w-[120px] md:min-w-[140px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                  >
                    {getModelOptions(selectedVideoModel).durations.map(duration => (
                      <option key={duration} value={duration}>{duration} seconds</option>
                    ))}
                  </select>
                </div>
                <SelectArrow />
              </div>

              {/* Video Aspect Ratio Control */}
              <div className="relative">
                <div
                  className="rounded-full p-[0.5px]"
                  style={{
                    background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                  }}
                >
                  <select
                    value={videoAspectRatio}
                    onChange={(e) => setVideoAspectRatio(e.target.value)}
                    className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[100px] sm:min-w-[120px] md:min-w-[140px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                  >
                    {getModelOptions(selectedVideoModel).aspectRatios.map(ratio => (
                      <option key={ratio} value={ratio}>
                        {ratio} {ratio === "16:9" ? "(Landscape)" : ratio === "9:16" ? "(Portrait)" : "(Square)"}
                      </option>
                    ))}
                  </select>
                </div>
                <SelectArrow />
              </div>
            </>
          )}

          {/* Style and Ratio controls - only show for image generation modes */}
          {(activeMode === 'text-to-image' || activeMode === 'image-to-image') && (
            <>
              <div className="relative">
                <div
                  className="rounded-full p-[0.5px]"
                  style={{
                    background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                  }}
                >
                  <select
                    value={selectedStyle}
                    onChange={(e) => setSelectedStyle(e.target.value)}
                    className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[100px] sm:min-w-[120px] md:min-w-[140px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                  >
                    <option value="Photorealistic">Photorealistic</option>
                    <option value="Cinematic">Cinematic</option>
                    <option value="Digital Art">Digital Art</option>
                    <option value="Fantasy">Fantasy</option>
                    <option value="Anime">Anime</option>
                    <option value="Oil Painting">Oil Painting</option>
                    <option value="Watercolor">Watercolor</option>
                    <option value="Sketch">Sketch</option>
                    <option value="3D Render">3D Render</option>
                    <option value="Minimalist">Minimalist</option>
                    <option value="Vintage">Vintage</option>
                    <option value="Cyberpunk">Cyberpunk</option>
                    <option value="Abstract">Abstract</option>
                    <option value="Pop Art">Pop Art</option>
                    <option value="Surreal">Surreal</option>
                  </select>
                </div>
                <SelectArrow />
              </div>
              <div className="relative">
                <div
                  className="rounded-full p-[0.5px]"
                  style={{
                    background: 'linear-gradient(95.14deg, #FFFFFF -2.53%, #5359FF 95.42%)'
                  }}
                >
                  <select
                    value={selectedRatio}
                    onChange={(e) => setSelectedRatio(e.target.value)}
                    className="bg-[#030F0F] rounded-full px-2 sm:px-3 md:px-4 py-2 text-white text-xs sm:text-sm min-w-[80px] sm:min-w-[90px] md:min-w-[100px] appearance-none cursor-pointer hover:bg-[#0a0a1a] transition-colors w-full"
                  >
                    <option value="1:1">1:1 (Square)</option>
                    <option value="16:9">16:9 (Landscape)</option>
                    <option value="9:16">9:16 (Portrait)</option>
                    <option value="4:3">4:3 (Standard)</option>
                    <option value="3:2">3:2 (Photo)</option>
                    <option value="21:9">21:9 (Ultrawide)</option>
                  </select>
                </div>
                <SelectArrow />
              </div>
            </>
          )}
        </div>
      )}

      <div className={`bg-[#1a1a2e] border border-[#2a2a3e] rounded-3xl ${collapsed ? 'p-2 sm:p-3 md:p-3 mb-2 sm:mb-3' : 'p-3 sm:p-4 md:p-6 mb-6 sm:mb-8'}`}>

        {/* Main Input Area - Changes based on mode */}
        {!collapsed && (
          <div className="transition-all duration-300 ease-in-out">
            {activeMode === 'image-to-image' ? (
              <div className="mb-4 sm:mb-6 w-full">
                {/* Show uploaded image if available */}
                {uploadedImage && (
                  <div className="mb-4 relative inline-block">
                    <img
                      src={uploadedImage}
                      alt="Uploaded for image transformation"
                      className="w-full max-w-xs h-32 object-cover rounded-lg border border-[#2a2a3e]"
                    />
                    <button
                      onClick={() => setUploadedImage(null)}
                      className="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm transition-colors"
                      title="Remove image"
                    >
                      ×
                    </button>
                  </div>
                )}

                {/* Input area with icon on right */}
                <div className="flex items-center gap-2">
                  <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Describe the image transformation you want..."
                    className="flex-1 h-12 sm:h-14 bg-transparent border-none rounded-lg px-4 text-[#D1D1D1] text-sm resize-none focus:outline-none focus:ring-0 placeholder-[#888]"
                    style={{ background: 'none' }}
                  />
                  <button
                    onClick={() => document.getElementById('image-to-image-upload-input')?.click()}
                    className="flex items-center justify-center w-10 h-10 rounded-full bg-[#23234a] hover:bg-[#2d2d5a] transition-colors border border-[#39397a] mr-1"
                    type="button"
                    title="Upload image"
                  >
                    <ImagetoImageModalIcon />
                    <span className="ml-1 text-lg text-white font-bold">+</span>
                  </button>
                  <input
                    id="image-to-image-upload-input"
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                  <button
                    onClick={() => handleGenerate(uploadedImage)}
                    disabled={isGenerating || !prompt.trim() || !uploadedImage}
                    className="bg-[#887DFF] hover:bg-[#6c5ce7] disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-6 py-2 rounded-full text-base font-semibold transition-colors flex items-center gap-2 shadow-sm"
                  >
                    <GeneratebtnIcon />
                    Generate
                  </button>
                </div>
              </div>
            ) : activeMode === 'image-to-video' ? (
              <div className="mb-4 sm:mb-6 w-full">
                {/* Show uploaded image if available */}
                {uploadedImage && (
                  <div className="mb-4 relative inline-block">
                    <img
                      src={uploadedImage}
                      alt="Uploaded for video generation"
                      className="w-full max-w-xs h-32 object-cover rounded-lg border border-[#2a2a3e]"
                    />
                    <button
                      onClick={() => setUploadedImage(null)}
                      className="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm transition-colors"
                      title="Remove image"
                    >
                      ×
                    </button>
                  </div>
                )}

                {/* Input area with icon on right */}
                <div className="flex items-center gap-2">
                  <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Describe the video motion you want..."
                    className="flex-1 h-12 sm:h-14 bg-transparent border-none rounded-lg px-4 text-[#D1D1D1] text-sm resize-none focus:outline-none focus:ring-0 placeholder-[#888]"
                    style={{ background: 'none' }}
                  />
                  <button
                    onClick={() => document.getElementById('image-to-video-upload-input')?.click()}
                    className="flex items-center justify-center w-10 h-10 rounded-full bg-[#23234a] hover:bg-[#2d2d5a] transition-colors border border-[#39397a] mr-1"
                    type="button"
                    title="Upload image"
                  >
                    <ImagetoVideoModalIcon />
                    <span className="ml-1 text-lg text-white font-bold">+</span>
                  </button>
                  <input
                    id="image-to-video-upload-input"
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                  <button
                    onClick={() => handleGenerate(uploadedImage)}
                    disabled={isGenerating || !prompt.trim() || !uploadedImage}
                    className="bg-[#887DFF] hover:bg-[#6c5ce7] disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-6 py-2 rounded-full text-base font-semibold transition-colors flex items-center gap-2 shadow-sm"
                  >
                    <GeneratebtnIcon />
                    Generate
                  </button>
                </div>
              </div>
            ) : (activeMode === 'text-to-image' || activeMode === 'text-to-video' || activeMode === 'text-to-speech' || activeMode === 'sound-effects') ? (
              <div className="relative mb-4 sm:mb-6">
                <textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder={
                    activeMode === 'text-to-video' ? "Describe the video you want to create..." :
                      activeMode === 'text-to-speech' ? "Enter the text you want to convert to speech (up to 2000 characters)..." :
                        activeMode === 'sound-effects' ? "Describe the sound effect you want to create (e.g., 'Quiet speech and then an airplane flying away')..." :
                          "Describe what you want to create..."
                  }
                  className="w-full h-20 sm:h-24 bg-[#1a1a2e] border border-none rounded-lg p-3 sm:p-4 text-[#888888] text-xs sm:text-sm resize-none focus:border-[#4a4a6e] focus:outline-none transition-colors"
                />
                {/* Generate Button */}
                <button
                  onClick={() => handleGenerate(uploadedImage)}
                  disabled={isGenerating || !prompt.trim()}
                  className="absolute bottom-0 right-3 sm:right-4 bg-[#6366f1] hover:bg-[#5855eb] disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 sm:px-6 py-2 rounded-full text-xs sm:text-sm font-medium transition-colors flex items-center gap-2"
                >
                  {isGenerating ? (
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                  ) : (
                    <GeneratebtnIcon />
                  )}
                  {isGenerating ? 'Generating...' : 'Generate'}
                </button>
              </div>
            ) : activeMode === 'lip-sync' ? (
              // Lip Sync Mode - Show simple message
              <div className="relative mb-4 sm:mb-6">
                <div className="w-full h-20 sm:h-24 bg-inherit rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <button
                      onClick={handlePlusButtonClick}
                      className="flex flex-col items-center gap-2 p-3 sm:p-4 hover:bg-[#2a2a3e] rounded-lg transition-colors group"
                    >
                      <div className="w-8 h-8 bg-[#6366f1] rounded-full flex items-center justify-center group-hover:bg-[#5855eb] transition-colors">
                        <CircularaddbtnIcon />
                      </div>
                      <span className="text-gray-400 text-[10px] sm:text-xs font-medium group-hover:text-white transition-colors">
                        Add reference file and choose model
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              // Music Generation Plus Button Area
              <div className="relative mb-4 sm:mb-6">
                <div className="w-full h-20 sm:h-24 bg-inherit  rounded-lg flex items-center justify-center">
                  <button
                    onClick={handlePlusButtonClick}
                    className="flex flex-col items-center gap-2 p-3 sm:p-4 hover:bg-[#2a2a3e] rounded-lg transition-colors group"
                  >
                    <div className="w-8 h-8 bg-[#6366f1] rounded-full flex items-center justify-center group-hover:bg-[#5855eb] transition-colors">
                      <CircularaddbtnIcon />
                    </div>
                    <span className="text-gray-400 text-[10px] sm:text-xs font-medium group-hover:text-white transition-colors">
                      Add reference file and lyrics
                    </span>
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
        {/* Tool Selection Buttons inside the card */}
        <ToolSelectionButtons activeMode={activeMode} handleModeChange={handleModeChange} />
      </div>
    </div>

  );
};

export default ChatInputControls;
